{{#
    @name Perspectives by category Index
#}}

<!-- /perspective/perspectives_categories/index.antlers.html -->
<div class="content-area-a js-slider-over pt-10 bg-dark">
	{{ partial:layout/header theme="dark" }}
	<section class="c-text-feature z-30 relative snap-start pt-10">
		<div class="container px-4 pb-10 lg:pb-14">
			<h1 class="flex font-bold text-56 text-white">
				<div class="c-label-updated"></div>
				A look at some of<br />our work
			</h1>
			<div class="text-24 font-semibold mt-7 ml-[68px] max-w-[908px]">
				<p class="text-white mb-7">At Platinum Seed, we're focused on creating strategic work that resonates with our clients' customers.</p>
				<p class="text-white">Always seeking long lasting relationships, delivering measurable ROI and mutual growth with like-minded businesses.</p>
			</div>
		</div>
	</section>
</div>

{{ entries as="items" }}
	<div class="content-area-b pt-6 rounded-t-lg -mt-7 z-20 relative bg-white">
		<div x-data="tagFilter" class="container px-4">
			<div class="c-tag-filter flex flex-wrap pb-5">
				<a href="/our-work/" class="text-dark px-4 py-1 border-b font-bold border-accent inline-block">
					<span class="inline-block" style="width: max-content;">
						<span class="block font-bold opacity-0 h-0 overflow-hidden" aria-hidden="true">All</span>
						<span class="block -mt-[1.25rem]">All</span>
					</span>
				</a>
				{{ taxonomy from="case_study_categories" }}
					<a href="/our-work/case-study-categories/{{slug}}/" class="text-dark px-4 py-1 border-b border-grey hover:font-bold hover:border-accent transition-all duration-300 ease-linear inline-block">
						<span class="inline-block" style="width: max-content;">
							<span class="block font-bold opacity-0 h-0 overflow-hidden" aria-hidden="true">{{title}}</span>
							<span class="block -mt-[1.25rem]">{{title}}</span>
						</span>
					</a>
				{{ /taxonomy }}
			</div>
			
			
			<div class="c-tag-filter flex flex-wrap gap-4 p-3 bg-[#F7F7F7] rounded-md mb-9">
				{{ taxonomy from="tags" slug:is="{{ slug }}" }}
					<div href="#" class="text-dark bg-white flex gap-2 items-center border border-grey-300 rounded-lg px-4 pt-[6px] pb-1">
						<div class="w-[18px] h-[18px] border border-grey-100 rounded-full bg-grey-300"></div>
						<span>{{title}}</span>
					</div>
				{{ /taxonomy }}
			</div>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 -mx-7 pb-9">
				{{ unless no_results }}
					{{ items }}
						<a 
							href="{{ url | ensure_right('/') }}" 
							class="c-card group rounded-lg bg-white hover:shadow-[0px 4px 98px rgba(0, 0, 0, 0.13)] relative transition-all duration-200 px-7 py-8 js-view-case-cursor" 
							data-bg="{{card_bg}}"
						>
							<div class="absolute top-0 right-0 group-hover:top-2 group-hover:-right-2 bg-grey-050 rounded-lg -z-10 w-full h-full opacity-0 group-hover:opacity-100 transition-all duration-200"></div>
							<div class="absolute top-0 right-0 group-hover:top-4 group-hover:-right-4 bg-grey-100 shadow-xl rounded-lg -z-20 w-full h-full opacity-0 group-hover:opacity-100 transition-all duration-200 delay-100"></div>
							{{ responsive:listing_page_image class="rounded-t-sm" :alt="title" }}
							{{ partial:components/seperator seperator_class="h-px my-6"}}
							<div class="{{ theme == 'dark' ? 'text-white' : 'text-dark' }} text-24 font-semibold mb-3">{{title}}</div>
							<div class="{{ theme == 'dark' ? 'text-white' : 'text-dark' }} text-32 font-bold mb-3">{{hero_title}}</div>
						</a>
					{{ /items }}
				{{ else }}
					<div class="md:col-span-6">
						{{ trans:strings.no_results }}
					</div>
				{{ /unless }}
			</div>
		</div>
	</div>
{{ /entries }}
{{ partial:layout/footer }}

<!-- End: /perspective/index.antlers.html -->