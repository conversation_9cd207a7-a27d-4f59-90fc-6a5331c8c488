tabs:
  main:
    display: Main
    sections:
      -
        fields:
          -
            handle: background_image
            field:
              container: images
              folder: services
              restrict: true
              type: assets
              display: Background
          -
            handle: title
            field:
              type: text
              required: true
              validate:
                - required
          -
            handle: header_description
            field:
              type: textarea
              display: 'Header description'
          -
            handle: listing_image
            field:
              use_breakpoints: false
              allow_ratio: false
              container: images
              folder: services
              restrict: true
              type: responsive
              display: 'Listing image'
          -
            handle: listing_description
            field:
              type: textarea
              display: 'Listing description'
              validate:
                - required
          -
            handle: detail_caption_line_1
            field:
              type: text
              display: 'Detail caption line 1'
              validate:
                - required
          -
            handle: detail_caption_line_2
            field:
              type: text
              display: 'Detail caption line 2'
              validate:
                - required
          -
            handle: testimonial
            field:
              type: group
              display: Testimonial
              fields:
                -
                  handle: client_name
                  field:
                    type: text
                    display: 'Client Name'
                -
                  handle: quote
                  field:
                    type: textarea
                    display: Quote
                -
                  handle: author
                  field:
                    type: text
                    display: Author
                -
                  handle: case_study
                  field:
                    collections:
                      - case_studies
                    type: link
                    display: 'Case Study'
          -
            handle: our_thinking_cards
            field:
              type: grid
              display: 'Our thinking cards'
              fields:
                -
                  handle: title
                  field:
                    type: text
                    display: Title
                    validate:
                      - required
                -
                  handle: tag
                  field:
                    max_items: 1
                    create: false
                    taxonomies:
                      - secondary_tags
                    type: terms
                    display: Tag
          -
            handle: featured_case_studies
            field:
              create: false
              collections:
                - case_studies
              type: entries
              display: 'Featured case studies'
          -
            handle: clients
            field:
              type: section
              display: Clients
          -
            handle: image_marqee_rows
            field: trusted_partners.image_marqee_rows
          -
            handle: faqs
            field:
              type: grid
              display: FAQs
              fields:
                -
                  handle: question
                  field:
                    input_type: text
                    antlers: false
                    type: text
                    display: Question
                    icon: text
                    listable: hidden
                    instructions_position: above
                    visibility: visible
                    hide_display: false
                    validate:
                      - required
                -
                  handle: answer
                  field:
                    input_type: text
                    antlers: false
                    type: text
                    display: Answer
                    icon: text
                    listable: hidden
                    instructions_position: above
                    visibility: visible
                    hide_display: false
                    validate:
                      - required
          -
            handle: listing_images
            field:
              min_files: 1
              container: images
              folder: services
              restrict: true
              type: assets
              display: 'Listing images'
  sidebar:
    display: Sidebar
    sections:
      -
        fields:
          -
            handle: slug
            field:
              type: slug
              localizable: true
              validate: 'max:200'
          -
            handle: tags
            field:
              type: terms
              taxonomies:
                - tags
              display: Tags
              mode: select
          -
            handle: date
            field:
              type: date
              required: true
              default: now
              validate:
                - required
title: Service
