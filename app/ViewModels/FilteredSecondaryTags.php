<?php

namespace App\ViewModels;

use Statamic\View\ViewModel;
use Statamic\Facades\Collection;

class FilteredSecondaryTags extends ViewModel
{
    public function data(): array
    {
		$filtered_tags = [];
		$tag_slugs = [];

		// Get the current taxonomy term slug from the cascade
		$current_category_slug = $this->cascade->get('slug');

		// Build the taxonomy filter dynamically based on current secondary tag
		$taxonomy_filter = 'secondary_tags::' . $current_category_slug;

		// Use the Collection facade to get entries from the case_studies collection
		// filtered by the current secondary tag
        $entries = Collection::find('case_studies')
            ->queryEntries()
			->whereTaxonomy($taxonomy_filter)
            ->get();

		foreach($entries as $entry) {
			// Check if the entry has tags before trying to iterate
			if ($entry->tags) {
				foreach($entry->tags as $tag) {
					// Get the tag slug (handle)
					$tag_slug = $tag->slug();

					// Check if the tag is not already in the array
					if (!in_array($tag_slug, $tag_slugs)) {
						// Add the tag slug to our tracking array
						$tag_slugs[] = $tag_slug;

						// Add the tag object with slug and title to the filtered_tags array
						$filtered_tags[] = [
							'slug' => $tag_slug,
							'title' => $tag->title()
						];
					}
				}
			}
		}

		// Debug: uncomment the line below to see what's being returned
		// dd(['current_category_slug' => $current_category_slug, 'filtered_tags' => $filtered_tags, 'entries_count' => $entries->count()]);

        return [
            'filtered_tags' => $filtered_tags
        ];
    }
}
